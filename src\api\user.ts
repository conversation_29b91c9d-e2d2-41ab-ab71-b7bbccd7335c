import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export type UserResult = {
  success: boolean;
  data: {
    /** 头像 */
    avatar: string;
    /** 用户名 */
    username: string;
    /** 昵称 */
    nickname: string;
    /** 当前登录用户的角色 */
    roles: Array<string>;
    /** 按钮级别权限 */
    permissions: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

// 后端登录接口返回的原始数据结构
export type LoginResponse = {
  success: boolean;
  message?: string;
  data?: {
    token: string;
    admin: {
      id: number;
      username: string;
      real_name?: string;
      email?: string;
      phone?: string;
      role: "super_admin" | "admin" | "editor";
      status: "active" | "inactive";
      last_login_at?: string;
      created_at: string;
      updated_at?: string;
    };
  };
};

export type AdminProfileResult = {
  success: boolean;
  data: {
    id: number;
    username: string;
    real_name?: string;
    email?: string;
    phone?: string;
    role: "super_admin" | "admin" | "editor";
    status: "active" | "inactive";
    last_login_at?: string;
    created_at: string;
    updated_at?: string;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/** 管理员登录 */
export const getLogin = (data?: object) => {
  return http.request<LoginResponse>("post", baseUrlApi("admin/login"), {
    data
  });
};

/** 转换登录响应数据为前端需要的格式 */
export const transformLoginResponse = (response: LoginResponse): UserResult => {
  if (!response.success || !response.data) {
    return {
      success: false,
      data: {
        avatar: "",
        username: "",
        nickname: "",
        roles: [],
        permissions: [],
        accessToken: "",
        refreshToken: "",
        expires: new Date()
      }
    };
  }

  const { token, admin } = response.data;

  // 根据角色设置权限
  const roles = [admin.role];
  const permissions = admin.role === "super_admin" ? ["*:*:*"] : [];

  // 设置 token 过期时间（默认 2 小时）
  const expires = new Date();
  expires.setHours(expires.getHours() + 2);

  return {
    success: true,
    data: {
      avatar: "", // 后端暂无头像字段，使用默认值
      username: admin.username,
      nickname: admin.real_name || admin.username,
      roles,
      permissions,
      accessToken: token,
      refreshToken: token, // 暂时使用同一个 token
      expires
    }
  };
};

/** 获取管理员信息 */
export const getAdminProfile = () => {
  return http.request<AdminProfileResult>("get", baseUrlApi("admin/profile"));
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", baseUrlApi("refresh-token"), {
    data
  });
};
